import type { documentUrlCrew, Project, questionResponseCrew } from './project';
import type { Files } from './workflow';

export type BriefAnalysisFlow = {
  brief_analysis_process?: string;
  brief_analysis_output?: string;
  client_brief_url?: Files[];
  project_id?: string;
  answer_brief?: string;
  answer_brief_process?: string;
};

export type summarizeFlow = {
  project_info?: Project;
  initial_info?: questionResponseCrew[];
  document_url?: documentUrlCrew[];
  client_summarize_process?: string;
};

export type assessmentStateFlow = {
  client_assessment_process?: string;
  risk_output: RiskOutput;
  engagement_output: EngagementOutput;
  priority_score_output: PriorityScoreOutput;
};

export type PriorityScoreOutput = {
  priorityScore: Dimension[];
  totalScore: string;
};

export type Dimension = {
  dimension: string;
  score: string;
  weight: string;
  weightedScore: string;
  comment: string;
};

export type EngagementOutput = {
  engagement_strategy: EngagementStrategy[];
};

export type EngagementStrategy = {
  dimension: string;
  action: string;
  kpi: string;
};

export type RiskOutput = {
  risk_assessment: RiskAssessment[];
};

export type RiskAssessment = {
  risk_name: string;
  justification: string;
};
